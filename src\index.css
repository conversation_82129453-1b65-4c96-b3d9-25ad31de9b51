@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-sans antialiased;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-transparent border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .section-padding {
    @apply py-16 md:py-24;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .dark .gradient-bg {
    background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
  }

  /* Background Animations */
  .bg-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
  }

  .floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .shape {
    position: absolute;
    opacity: 0.1;
    animation: float 20s infinite linear;
  }

  .shape.square {
    background: linear-gradient(45deg, #3b82f6, #8b5cf6);
    border-radius: 4px;
  }

  .shape.circle {
    background: linear-gradient(45deg, #06b6d4, #3b82f6);
    border-radius: 50%;
  }

  .shape.triangle {
    width: 0;
    height: 0;
    background: transparent;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 25px solid #8b5cf6;
    opacity: 0.1;
  }

  .dark .shape.square {
    background: linear-gradient(45deg, #1e40af, #7c3aed);
  }

  .dark .shape.circle {
    background: linear-gradient(45deg, #0891b2, #1e40af);
  }

  .dark .shape.triangle {
    border-bottom-color: #7c3aed;
  }

  @keyframes float {
    0% {
      transform: translateY(100vh) rotate(0deg);
    }
    100% {
      transform: translateY(-100px) rotate(360deg);
    }
  }

  .bubble {
    position: absolute;
    background: linear-gradient(
      45deg,
      rgba(59, 130, 246, 0.1),
      rgba(139, 92, 246, 0.1)
    );
    border-radius: 50%;
    animation: bubble 15s infinite linear;
  }

  .dark .bubble {
    background: linear-gradient(
      45deg,
      rgba(30, 64, 175, 0.1),
      rgba(124, 58, 237, 0.1)
    );
  }

  @keyframes bubble {
    0% {
      transform: translateY(100vh) scale(0);
      opacity: 0;
    }
    10% {
      opacity: 0.1;
    }
    90% {
      opacity: 0.1;
    }
    100% {
      transform: translateY(-100px) scale(1);
      opacity: 0;
    }
  }
}
