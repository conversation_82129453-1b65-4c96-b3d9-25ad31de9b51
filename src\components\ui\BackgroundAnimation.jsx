import { motion } from "framer-motion";

const BackgroundAnimation = () => {
  // Static arrays for consistent animations
  const squares = Array.from({ length: 12 }, (_, i) => ({
    id: i,
    size: Math.random() * 50 + 30,
    left: Math.random() * 100,
    delay: i * 2,
  }));

  const circles = Array.from({ length: 8 }, (_, i) => ({
    id: i,
    size: Math.random() * 80 + 40,
    left: Math.random() * 100,
    delay: i * 3,
  }));

  const particles = Array.from({ length: 20 }, (_, i) => ({
    id: i,
    size: Math.random() * 6 + 4,
    left: Math.random() * 100,
    top: Math.random() * 100,
    delay: i * 0.5,
  }));

  return (
    <div
      className="fixed inset-0 pointer-events-none overflow-hidden"
      style={{ zIndex: -1 }}
    >
      {/* Floating Squares */}
      {squares.map((square) => (
        <motion.div
          key={`square-${square.id}`}
          className="absolute bg-blue-500 dark:bg-blue-400 opacity-10 dark:opacity-20"
          style={{
            width: `${square.size}px`,
            height: `${square.size}px`,
            left: `${square.left}%`,
            borderRadius: "8px",
          }}
          animate={{
            y: [-100, window.innerHeight + 100],
            rotate: [0, 360],
            x: [0, Math.sin(square.id) * 100],
          }}
          transition={{
            duration: 25,
            delay: square.delay,
            repeat: Infinity,
            ease: "linear",
          }}
        />
      ))}

      {/* Floating Circles/Bubbles */}
      {circles.map((circle) => (
        <motion.div
          key={`circle-${circle.id}`}
          className="absolute bg-purple-500 dark:bg-purple-400 opacity-10 dark:opacity-20 rounded-full border border-purple-300 dark:border-purple-500"
          style={{
            width: `${circle.size}px`,
            height: `${circle.size}px`,
            left: `${circle.left}%`,
          }}
          animate={{
            y: [-100, window.innerHeight + 100],
            scale: [0.5, 1, 0.8, 1],
            x: [0, Math.cos(circle.id) * 80],
          }}
          transition={{
            duration: 20,
            delay: circle.delay,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}

      {/* Floating Particles */}
      {particles.map((particle) => (
        <motion.div
          key={`particle-${particle.id}`}
          className="absolute bg-cyan-400 dark:bg-cyan-300 rounded-full opacity-40 dark:opacity-60"
          style={{
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            left: `${particle.left}%`,
            top: `${particle.top}%`,
          }}
          animate={{
            scale: [0, 1, 0],
            opacity: [0, 0.6, 0],
            y: [0, -50, 0],
          }}
          transition={{
            duration: 6,
            delay: particle.delay,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}

      {/* Large Background Orbs */}
      <motion.div
        className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-300 to-purple-300 dark:from-blue-800 dark:to-purple-800 rounded-full opacity-20 dark:opacity-30 blur-3xl"
        animate={{
          scale: [1, 1.2, 1],
          x: [0, 100, 0],
          y: [0, -50, 0],
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <motion.div
        className="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-r from-pink-300 to-cyan-300 dark:from-pink-800 dark:to-cyan-800 rounded-full opacity-20 dark:opacity-30 blur-3xl"
        animate={{
          scale: [1, 0.8, 1.3, 1],
          x: [0, -80, 0],
          y: [0, 60, 0],
        }}
        transition={{
          duration: 18,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    </div>
  );
};

export default BackgroundAnimation;
