import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

const BackgroundAnimation = () => {
  const [shapes, setShapes] = useState([]);
  const [bubbles, setBubbles] = useState([]);

  useEffect(() => {
    // Generate floating shapes
    const generateShapes = () => {
      const newShapes = [];
      for (let i = 0; i < 15; i++) {
        newShapes.push({
          id: i,
          type: ['square', 'circle', 'triangle'][Math.floor(Math.random() * 3)],
          size: Math.random() * 30 + 20,
          left: Math.random() * 100,
          delay: Math.random() * 20,
          duration: Math.random() * 10 + 15,
        });
      }
      setShapes(newShapes);
    };

    // Generate bubbles
    const generateBubbles = () => {
      const newBubbles = [];
      for (let i = 0; i < 10; i++) {
        newBubbles.push({
          id: i,
          size: Math.random() * 60 + 40,
          left: Math.random() * 100,
          delay: Math.random() * 15,
          duration: Math.random() * 5 + 10,
        });
      }
      setBubbles(newBubbles);
    };

    generateShapes();
    generateBubbles();
  }, []);

  return (
    <div className="bg-animation">
      {/* Floating Shapes */}
      <div className="floating-shapes">
        {shapes.map((shape) => (
          <motion.div
            key={shape.id}
            className={`shape ${shape.type}`}
            style={{
              width: shape.type !== 'triangle' ? `${shape.size}px` : 'auto',
              height: shape.type !== 'triangle' ? `${shape.size}px` : 'auto',
              left: `${shape.left}%`,
            }}
            animate={{
              y: [window.innerHeight + 100, -100],
              rotate: [0, 360],
              x: [0, Math.sin(shape.id) * 50],
            }}
            transition={{
              duration: shape.duration,
              delay: shape.delay,
              repeat: Infinity,
              ease: "linear",
            }}
          />
        ))}
      </div>

      {/* Floating Bubbles */}
      <div className="floating-shapes">
        {bubbles.map((bubble) => (
          <motion.div
            key={`bubble-${bubble.id}`}
            className="bubble"
            style={{
              width: `${bubble.size}px`,
              height: `${bubble.size}px`,
              left: `${bubble.left}%`,
            }}
            animate={{
              y: [window.innerHeight + 100, -100],
              scale: [0, 1, 0.8, 1],
              opacity: [0, 0.1, 0.1, 0],
            }}
            transition={{
              duration: bubble.duration,
              delay: bubble.delay,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      {/* Gradient Orbs */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-300 dark:bg-blue-900 rounded-full mix-blend-multiply dark:mix-blend-screen filter blur-xl opacity-20"
          animate={{
            scale: [1, 1.2, 1],
            x: [0, 50, 0],
            y: [0, -30, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute top-1/3 right-1/4 w-80 h-80 bg-purple-300 dark:bg-purple-900 rounded-full mix-blend-multiply dark:mix-blend-screen filter blur-xl opacity-20"
          animate={{
            scale: [1, 0.8, 1.1, 1],
            x: [0, -40, 0],
            y: [0, 40, 0],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-pink-300 dark:bg-pink-900 rounded-full mix-blend-multiply dark:mix-blend-screen filter blur-xl opacity-20"
          animate={{
            scale: [1, 1.3, 0.9, 1],
            x: [0, 30, 0],
            y: [0, -50, 0],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </div>

      {/* Particle Grid */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={`particle-${i}`}
            className="absolute w-1 h-1 bg-blue-400 dark:bg-blue-300 rounded-full opacity-30"
            style={{
              left: `${(i % 5) * 20 + 10}%`,
              top: `${Math.floor(i / 5) * 25 + 10}%`,
            }}
            animate={{
              scale: [0, 1, 0],
              opacity: [0, 0.3, 0],
            }}
            transition={{
              duration: 3,
              delay: i * 0.2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default BackgroundAnimation;
