import { useEffect, useState } from "react";
import { motion } from "framer-motion";

const BackgroundAnimation = () => {
  const [shapes, setShapes] = useState([]);
  const [bubbles, setBubbles] = useState([]);

  useEffect(() => {
    // Generate floating shapes
    const generateShapes = () => {
      const newShapes = [];
      for (let i = 0; i < 15; i++) {
        newShapes.push({
          id: i,
          type: ["square", "circle", "triangle"][Math.floor(Math.random() * 3)],
          size: Math.random() * 30 + 20,
          left: Math.random() * 100,
          delay: Math.random() * 20,
          duration: Math.random() * 10 + 15,
        });
      }
      setShapes(newShapes);
    };

    // Generate bubbles
    const generateBubbles = () => {
      const newBubbles = [];
      for (let i = 0; i < 10; i++) {
        newBubbles.push({
          id: i,
          size: Math.random() * 60 + 40,
          left: Math.random() * 100,
          delay: Math.random() * 15,
          duration: Math.random() * 5 + 10,
        });
      }
      setBubbles(newBubbles);
    };

    generateShapes();
    generateBubbles();
  }, []);

  return (
    <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
      {/* Floating Shapes */}
      <div className="absolute inset-0">
        {shapes.map((shape) => (
          <motion.div
            key={shape.id}
            className="absolute"
            style={{
              width: shape.type !== "triangle" ? `${shape.size}px` : "30px",
              height: shape.type !== "triangle" ? `${shape.size}px` : "30px",
              left: `${shape.left}%`,
              background:
                shape.type === "square"
                  ? "linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1))"
                  : shape.type === "circle"
                  ? "linear-gradient(45deg, rgba(6, 182, 212, 0.1), rgba(59, 130, 246, 0.1))"
                  : "transparent",
              borderRadius:
                shape.type === "circle"
                  ? "50%"
                  : shape.type === "square"
                  ? "4px"
                  : "0",
              borderLeft:
                shape.type === "triangle" ? "15px solid transparent" : "none",
              borderRight:
                shape.type === "triangle" ? "15px solid transparent" : "none",
              borderBottom:
                shape.type === "triangle"
                  ? "25px solid rgba(139, 92, 246, 0.1)"
                  : "none",
            }}
            animate={{
              y: [window.innerHeight + 100, -100],
              rotate: [0, 360],
              x: [0, Math.sin(shape.id) * 50],
            }}
            transition={{
              duration: shape.duration,
              delay: shape.delay,
              repeat: Infinity,
              ease: "linear",
            }}
          />
        ))}
      </div>

      {/* Floating Bubbles */}
      <div className="absolute inset-0">
        {bubbles.map((bubble) => (
          <motion.div
            key={`bubble-${bubble.id}`}
            className="absolute rounded-full"
            style={{
              width: `${bubble.size}px`,
              height: `${bubble.size}px`,
              left: `${bubble.left}%`,
              background:
                "linear-gradient(45deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05))",
              border: "1px solid rgba(59, 130, 246, 0.1)",
            }}
            animate={{
              y: [window.innerHeight + 100, -100],
              scale: [0, 1, 0.8, 1],
              opacity: [0, 0.3, 0.3, 0],
            }}
            transition={{
              duration: bubble.duration,
              delay: bubble.delay,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      {/* Gradient Orbs */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full filter blur-xl opacity-30"
          style={{
            background:
              "radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, rgba(59, 130, 246, 0.1) 50%, transparent 100%)",
          }}
          animate={{
            scale: [1, 1.2, 1],
            x: [0, 50, 0],
            y: [0, -30, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute top-1/3 right-1/4 w-80 h-80 rounded-full filter blur-xl opacity-30"
          style={{
            background:
              "radial-gradient(circle, rgba(139, 92, 246, 0.3) 0%, rgba(139, 92, 246, 0.1) 50%, transparent 100%)",
          }}
          animate={{
            scale: [1, 0.8, 1.1, 1],
            x: [0, -40, 0],
            y: [0, 40, 0],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute bottom-1/4 left-1/3 w-72 h-72 rounded-full filter blur-xl opacity-30"
          style={{
            background:
              "radial-gradient(circle, rgba(236, 72, 153, 0.3) 0%, rgba(236, 72, 153, 0.1) 50%, transparent 100%)",
          }}
          animate={{
            scale: [1, 1.3, 0.9, 1],
            x: [0, 30, 0],
            y: [0, -50, 0],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </div>

      {/* Particle Grid */}
      <div className="absolute inset-0">
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={`particle-${i}`}
            className="absolute w-2 h-2 rounded-full"
            style={{
              left: `${(i % 5) * 20 + 10}%`,
              top: `${Math.floor(i / 5) * 20 + 10}%`,
              background:
                "radial-gradient(circle, rgba(59, 130, 246, 0.6) 0%, rgba(59, 130, 246, 0.2) 100%)",
              boxShadow: "0 0 10px rgba(59, 130, 246, 0.3)",
            }}
            animate={{
              scale: [0, 1, 0],
              opacity: [0, 0.8, 0],
            }}
            transition={{
              duration: 4,
              delay: i * 0.15,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default BackgroundAnimation;
