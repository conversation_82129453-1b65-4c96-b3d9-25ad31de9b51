import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";
import { FiDownload } from "react-icons/fi";
import {
  SiHtml5,
  SiCss3,
  SiJavascript,
  SiReact,
  SiTailwindcss,
  SiGit,
  <PERSON><PERSON><PERSON><PERSON>,
} from "react-icons/si";
import { FiSmartphone } from "react-icons/fi";
import {
  personalInfo,
  skillCategories,
  education,
} from "../../data/portfolioData";

const About = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });

  const skillIcons = {
    html: SiHtml5,
    css: SiCss3,
    javascript: SiJavascript,
    react: SiReact,
    tailwind: SiTailwindcss,
    git: SiGit,
    responsive: FiSmartphone,
    motion: SiFramer,
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <section id="about" className="section-padding bg-white dark:bg-dark-900">
      <div className="container-custom">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              About Me
            </h2>
            <div className="w-20 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full mb-4"></div>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Passionate developer with a love for creating digital solutions
              that make a difference
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Bio and Education */}
            <motion.div variants={itemVariants} className="space-y-8">
              {/* Bio */}
              <div>
                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                  Get to know me!
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed mb-6">
                  {personalInfo.bio}
                </p>
                <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed mb-6">
                  I'm passionate about creating user-friendly interfaces and
                  solving complex problems through code. I enjoy learning new
                  technologies and staying up-to-date with the latest trends in
                  web development.
                </p>

                <motion.a
                  href={personalInfo.resumeUrl}
                  download
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="btn-primary inline-flex items-center gap-2"
                >
                  <FiDownload />
                  Download Resume
                </motion.a>
              </div>

              {/* Education */}
              <motion.div
                variants={itemVariants}
                className="bg-white dark:bg-dark-900 p-6 rounded-lg shadow-lg"
              >
                <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Education
                </h4>
                <div className="space-y-2">
                  <h5 className="text-lg font-medium text-primary-600 dark:text-primary-400">
                    {education.degree}
                  </h5>
                  <p className="text-gray-600 dark:text-gray-300">
                    {education.institution}
                  </p>
                  <p className="text-gray-500 dark:text-gray-400">
                    {education.duration} • GPA: {education.gpa}
                  </p>
                  <div className="mt-4">
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Relevant Courses:
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {education.relevantCourses.map((course, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 text-sm rounded-full"
                        >
                          {course}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Column - Professional Summary */}
            <motion.div variants={itemVariants} className="space-y-8">
              <div className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-dark-800 dark:to-dark-700 p-8 rounded-xl">
                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
                  Professional Summary
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  Highly motivated Frontend Developer with robust foundation in modern web technologies.
                  Proven track record in developing responsive, scalable, and user-friendly web applications.
                  A rapid learner, adept problem-solver, and collaborative team player, dedicated to delivering
                  innovative web solutions and driving technological advancement.
                </p>
              </div>
            </motion.div>
          </div>

          {/* Skills Section */}
          <motion.div variants={itemVariants} className="mt-20">
            <div className="text-center mb-12">
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Skills & Expertise
              </h3>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Technologies and tools I work with to create amazing digital experiences
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {skillCategories.map((category, categoryIndex) => (
                <motion.div
                  key={category.title}
                  variants={itemVariants}
                  className="bg-white dark:bg-dark-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                >
                  <div className="text-center mb-6">
                    <div className="text-4xl mb-3">{category.icon}</div>
                    <h4 className="text-xl font-semibold text-gray-900 dark:text-white">
                      {category.title}
                    </h4>
                  </div>

                  <div className="space-y-4">
                    {category.skills.map((skill, skillIndex) => (
                      <div key={skill.name} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {skill.name}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {skill.level}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-dark-700 rounded-full h-2">
                          <motion.div
                            initial={{ width: 0 }}
                            animate={isInView ? { width: `${skill.level}%` } : { width: 0 }}
                            transition={{
                              duration: 1,
                              delay: categoryIndex * 0.2 + skillIndex * 0.1
                            }}
                            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
