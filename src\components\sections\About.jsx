import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import { FiDownload } from 'react-icons/fi';
import { 
  SiHtml5, SiCss3, SiJavascript, SiReact, SiTailwindcss, 
  SiGit, SiResponsive, <PERSON><PERSON><PERSON><PERSON> 
} from 'react-icons/si';
import { personalInfo, skills, education } from '../../data/portfolioData';

const About = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });

  const skillIcons = {
    html: SiHtml5,
    css: SiCss3,
    javascript: SiJavascript,
    react: SiReact,
    tailwind: SiTailwindcss,
    git: SiGit,
    responsive: SiResponsive,
    motion: <PERSON><PERSON>ramer
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <section id="about" className="section-padding bg-gray-50 dark:bg-dark-800">
      <div className="container-custom">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              About Me
            </h2>
            <div className="w-20 h-1 bg-primary-600 mx-auto rounded-full"></div>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Bio and Education */}
            <motion.div variants={itemVariants} className="space-y-8">
              {/* Bio */}
              <div>
                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                  Get to know me!
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed mb-6">
                  {personalInfo.bio}
                </p>
                <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed mb-6">
                  I'm passionate about creating user-friendly interfaces and solving complex problems 
                  through code. I enjoy learning new technologies and staying up-to-date with the 
                  latest trends in web development.
                </p>
                
                <motion.a
                  href={personalInfo.resumeUrl}
                  download
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="btn-primary inline-flex items-center gap-2"
                >
                  <FiDownload />
                  Download Resume
                </motion.a>
              </div>

              {/* Education */}
              <motion.div
                variants={itemVariants}
                className="bg-white dark:bg-dark-900 p-6 rounded-lg shadow-lg"
              >
                <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Education
                </h4>
                <div className="space-y-2">
                  <h5 className="text-lg font-medium text-primary-600 dark:text-primary-400">
                    {education.degree}
                  </h5>
                  <p className="text-gray-600 dark:text-gray-300">
                    {education.institution}
                  </p>
                  <p className="text-gray-500 dark:text-gray-400">
                    {education.duration} • GPA: {education.gpa}
                  </p>
                  <div className="mt-4">
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Relevant Courses:
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {education.relevantCourses.map((course, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 text-sm rounded-full"
                        >
                          {course}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Column - Skills */}
            <motion.div variants={itemVariants} className="space-y-8">
              <div>
                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-8">
                  My Skills
                </h3>
                
                <div className="grid grid-cols-2 gap-6">
                  {skills.map((skill, index) => {
                    const IconComponent = skillIcons[skill.icon];
                    
                    return (
                      <motion.div
                        key={skill.name}
                        variants={itemVariants}
                        whileHover={{ scale: 1.05 }}
                        className="bg-white dark:bg-dark-900 p-6 rounded-lg shadow-lg text-center group"
                      >
                        <div className="flex justify-center mb-4">
                          {IconComponent && (
                            <IconComponent 
                              size={40} 
                              className="text-primary-600 dark:text-primary-400 group-hover:scale-110 transition-transform duration-200" 
                            />
                          )}
                        </div>
                        
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                          {skill.name}
                        </h4>
                        
                        {/* Skill Level Bar */}
                        <div className="w-full bg-gray-200 dark:bg-dark-700 rounded-full h-2 mb-2">
                          <motion.div
                            initial={{ width: 0 }}
                            animate={isInView ? { width: `${skill.level}%` } : { width: 0 }}
                            transition={{ duration: 1, delay: index * 0.1 }}
                            className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full"
                          />
                        </div>
                        
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {skill.level}%
                        </span>
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
