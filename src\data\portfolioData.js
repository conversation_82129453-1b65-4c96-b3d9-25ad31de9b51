export const personalInfo = {
  name: "<PERSON><PERSON>",
  title: "Frontend Developer",
  subtitle:
    "Passionate about creating digital solutions that make a difference",
  bio: "I'm a passionate Frontend Developer with a strong foundation in modern web technologies. My journey in technology began with curiosity and has evolved into a deep commitment to creating innovative web solutions.",
  detailedBio:
    "With a proven track record in developing responsive, scalable, and user-friendly web applications, I bring both technical excellence and creative problem-solving to every project. As a rapid learner and collaborative team player, I'm dedicated to driving technological advancement and delivering solutions that make a real impact.",
  email: "<EMAIL>",
  phone: "+****************",
  location: "Your City, Country",
  resumeUrl: "/resume.pdf",
  profileImage: "/profile.png",
};

export const socialLinks = {
  github: "https://github.com/yourusername",
  linkedin: "https://linkedin.com/in/yourusername",
  twitter: "https://twitter.com/yourusername",
  email: "mailto:<EMAIL>",
};

export const skillCategories = [
  {
    title: "Frontend Development",
    icon: "🎨",
    skills: [
      { name: "React.js", level: 90 },
      { name: "JavaScript (ES6+)", level: 95 },
      { name: "HTML/CSS", level: 95 },
      { name: "Tailwind CSS", level: 85 },
      { name: "Bootstrap", level: 80 },
      { name: "Responsive Design", level: 90 },
    ],
  },
  {
    title: "Backend Development",
    icon: "⚙️",
    skills: [
      { name: "Node.js", level: 75 },
      { name: "Express.js", level: 75 },
      { name: "RESTful APIs", level: 80 },
      { name: "JWT Authentication", level: 70 },
    ],
  },
  {
    title: "Database & Tools",
    icon: "🛠️",
    skills: [
      { name: "MongoDB", level: 70 },
      { name: "MySQL", level: 65 },
      { name: "Git", level: 90 },
      { name: "Visual Studio Code", level: 95 },
    ],
  },
  {
    title: "Design & Animation",
    icon: "🎭",
    skills: [
      { name: "Framer Motion", level: 80 },
      { name: "CSS Animations", level: 85 },
      { name: "UI/UX Design", level: 75 },
      { name: "Figma", level: 70 },
    ],
  },
];

export const projects = [
  {
    id: 1,
    title: "E-Commerce Website",
    description:
      "A modern e-commerce platform built with React and TailwindCSS featuring product catalog, shopping cart, and checkout functionality.",
    image: "/project1.jpg",
    technologies: ["React", "TailwindCSS", "JavaScript", "Local Storage"],
    liveDemo: "https://your-ecommerce-demo.com",
    github: "https://github.com/yourusername/ecommerce-project",
    challenges:
      "Implementing complex state management for cart functionality and creating responsive product grids.",
    solutions:
      "Used React Context API for state management and CSS Grid with Flexbox for responsive layouts.",
  },
  {
    id: 2,
    title: "Weather Dashboard",
    description:
      "A responsive weather application that displays current weather and 5-day forecast using OpenWeatherMap API.",
    image: "/project2.jpg",
    technologies: ["HTML", "CSS", "JavaScript", "Weather API"],
    liveDemo: "https://your-weather-demo.com",
    github: "https://github.com/yourusername/weather-dashboard",
    challenges:
      "Handling API responses and creating dynamic weather icons based on conditions.",
    solutions:
      "Implemented error handling for API calls and created a mapping system for weather condition icons.",
  },
  {
    id: 3,
    title: "Task Management App",
    description:
      "A productivity app for managing daily tasks with features like add, edit, delete, and mark as complete.",
    image: "/project3.jpg",
    technologies: ["React", "CSS Modules", "Local Storage"],
    liveDemo: "https://your-taskapp-demo.com",
    github: "https://github.com/yourusername/task-manager",
    challenges:
      "Creating smooth animations for task interactions and persistent data storage.",
    solutions:
      "Used CSS transitions for animations and localStorage for data persistence.",
  },
  {
    id: 4,
    title: "Restaurant Landing Page",
    description:
      "A beautiful and responsive landing page for a restaurant with menu showcase and reservation system.",
    image: "/project4.jpg",
    technologies: ["HTML", "SCSS", "JavaScript", "AOS Library"],
    liveDemo: "https://your-restaurant-demo.com",
    github: "https://github.com/yourusername/restaurant-landing",
    challenges:
      "Creating engaging scroll animations and optimizing images for web performance.",
    solutions:
      "Implemented AOS library for scroll animations and used WebP format for optimized images.",
  },
];

export const experiences = [
  {
    id: 1,
    type: "project",
    title: "Personal Portfolio Development",
    organization: "Self-Directed",
    duration: "2024",
    description:
      "Designed and developed a responsive portfolio website showcasing frontend development skills using modern technologies.",
    achievements: [
      "Built responsive design with mobile-first approach",
      "Implemented dark mode functionality",
      "Integrated contact form with email service",
    ],
  },
  {
    id: 2,
    type: "learning",
    title: "Frontend Development Bootcamp",
    organization: "Online Learning Platform",
    duration: "2023 - 2024",
    description:
      "Completed comprehensive frontend development course covering HTML, CSS, JavaScript, and React.",
    achievements: [
      "Built 10+ projects from scratch",
      "Learned modern development workflows",
      "Collaborated on team projects",
    ],
  },
];

export const certifications = [
  {
    id: 1,
    name: "Responsive Web Design",
    issuer: "freeCodeCamp",
    date: "2024",
    credentialUrl:
      "https://freecodecamp.org/certification/yourusername/responsive-web-design",
  },
  {
    id: 2,
    name: "JavaScript Algorithms and Data Structures",
    issuer: "freeCodeCamp",
    date: "2024",
    credentialUrl:
      "https://freecodecamp.org/certification/yourusername/javascript-algorithms-and-data-structures",
  },
  {
    id: 3,
    name: "Frontend Development Libraries",
    issuer: "freeCodeCamp",
    date: "2024",
    credentialUrl:
      "https://freecodecamp.org/certification/yourusername/front-end-development-libraries",
  },
];

export const education = {
  degree: "Bachelor of Computer Science",
  institution: "Your University",
  duration: "2020 - 2024",
  gpa: "3.8/4.0",
  relevantCourses: [
    "Web Development",
    "Data Structures & Algorithms",
    "Database Management",
    "Software Engineering",
  ],
};
