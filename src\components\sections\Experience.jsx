import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, FiBook<PERSON><PERSON>, FiExternalLink } from 'react-icons/fi';
import { experiences, certifications } from '../../data/portfolioData';

const Experience = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const timelineVariants = {
    hidden: { scaleY: 0 },
    visible: {
      scaleY: 1,
      transition: {
        duration: 0.8,
        ease: "easeInOut"
      }
    }
  };

  return (
    <section id="experience" className="section-padding bg-gray-50 dark:bg-dark-800">
      <div className="container-custom">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Experience & Certifications
            </h2>
            <div className="w-20 h-1 bg-primary-600 mx-auto rounded-full mb-4"></div>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              My journey in frontend development and continuous learning
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Experience Timeline */}
            <motion.div variants={itemVariants}>
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-8 flex items-center gap-3">
                <FiBookOpen className="text-primary-600 dark:text-primary-400" />
                Experience & Projects
              </h3>
              
              <div className="relative">
                {/* Timeline Line */}
                <motion.div
                  variants={timelineVariants}
                  className="absolute left-4 top-0 bottom-0 w-0.5 bg-primary-600 origin-top"
                />

                <div className="space-y-8">
                  {experiences.map((experience, index) => (
                    <motion.div
                      key={experience.id}
                      variants={itemVariants}
                      className="relative flex items-start gap-6"
                    >
                      {/* Timeline Dot */}
                      <div className="relative z-10">
                        <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                          <div className="w-3 h-3 bg-white rounded-full"></div>
                        </div>
                      </div>

                      {/* Experience Card */}
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        className="flex-1 bg-white dark:bg-dark-900 p-6 rounded-lg shadow-lg"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <span className="px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 text-sm rounded-full">
                            {experience.type === 'project' ? 'Project' : 'Learning'}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {experience.duration}
                          </span>
                        </div>
                        
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                          {experience.title}
                        </h4>
                        
                        <p className="text-primary-600 dark:text-primary-400 font-medium mb-3">
                          {experience.organization}
                        </p>
                        
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                          {experience.description}
                        </p>
                        
                        {/* Achievements */}
                        <div>
                          <h5 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                            Key Achievements:
                          </h5>
                          <ul className="space-y-1">
                            {experience.achievements.map((achievement, achievementIndex) => (
                              <li
                                key={achievementIndex}
                                className="text-sm text-gray-600 dark:text-gray-400 flex items-start gap-2"
                              >
                                <span className="text-primary-600 dark:text-primary-400 mt-1">•</span>
                                {achievement}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </motion.div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Certifications */}
            <motion.div variants={itemVariants}>
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-8 flex items-center gap-3">
                <FiAward className="text-primary-600 dark:text-primary-400" />
                Certifications
              </h3>
              
              <div className="space-y-6">
                {certifications.map((cert, index) => (
                  <motion.div
                    key={cert.id}
                    variants={itemVariants}
                    whileHover={{ scale: 1.02, x: 10 }}
                    className="bg-white dark:bg-dark-900 p-6 rounded-lg shadow-lg border-l-4 border-primary-600"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                          {cert.name}
                        </h4>
                        <p className="text-primary-600 dark:text-primary-400 font-medium">
                          {cert.issuer}
                        </p>
                      </div>
                      <span className="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-dark-800 px-3 py-1 rounded-full">
                        {cert.date}
                      </span>
                    </div>
                    
                    <motion.a
                      href={cert.credentialUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05 }}
                      className="inline-flex items-center gap-2 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors duration-200 text-sm font-medium"
                    >
                      <FiExternalLink size={14} />
                      View Credential
                    </motion.a>
                  </motion.div>
                ))}
                
                {/* Call to Action */}
                <motion.div
                  variants={itemVariants}
                  className="bg-gradient-to-r from-primary-500 to-primary-600 p-6 rounded-lg text-white text-center"
                >
                  <h4 className="text-lg font-semibold mb-2">
                    Continuous Learning
                  </h4>
                  <p className="text-primary-100 mb-4">
                    I'm always learning new technologies and improving my skills. 
                    Currently exploring advanced React patterns and backend development.
                  </p>
                  <div className="flex flex-wrap gap-2 justify-center">
                    <span className="px-3 py-1 bg-white/20 rounded-full text-sm">
                      Next.js
                    </span>
                    <span className="px-3 py-1 bg-white/20 rounded-full text-sm">
                      TypeScript
                    </span>
                    <span className="px-3 py-1 bg-white/20 rounded-full text-sm">
                      Node.js
                    </span>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Experience;
